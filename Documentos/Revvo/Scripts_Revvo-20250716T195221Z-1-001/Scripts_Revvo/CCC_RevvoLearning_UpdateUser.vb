#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
#End If

Public Function CCC_RevvoLearning_Update(ByVal UID_UNSAccountB As String) As String
    Dim logMessage As String = ""

    Dim clientId As String = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\ClientId")
    Dim clientSecret As String = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\ClientSecret")

    Try
        logMessage = String.Format("[{0}] Iniciando atualização de usuário para UID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), UID_UNSAccountB)
        Console.WriteLine(logMessage)

        Dim f = Session.SqlFormatter
        Dim queryUNSAccountB = f.UidComparison("UID_UNSAccountB", UID_UNSAccountB)
        Dim UNSAccountBCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(queryUNSAccountB).SelectNonLobs)

        If UNSAccountBCol.Count > 0 Then
            Dim UNSAccountB As IEntity = UNSAccountBCol(0)

            Dim externalUserId As String = UNSAccountB.GetValue("CCC_CustomProperty24").String
            
            If String.IsNullOrEmpty(externalUserId) Then
                logMessage = String.Format("[{0}] ERRO: ID externo não encontrado no campo CCC_CustomProperty24", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                Console.WriteLine(logMessage)
                Return "ERRO: ID externo não encontrado"
            End If

            Dim username As String = UNSAccountB.GetValue("CCC_CustomProperty11").String
            Dim firstname As String = UNSAccountB.GetValue("FirstName").String
            Dim lastname As String = UNSAccountB.GetValue("LastName").String
            Dim email As String = UNSAccountB.GetValue("CCC_CustomProperty15").String
            Dim department As String = UNSAccountB.GetValue("CCC_CustomProperty03").String
            Dim institution As String = UNSAccountB.GetValue("CCC_CustomProperty19").String
            Dim phone1 As String = UNSAccountB.GetValue("CCC_CustomProperty25").String

            logMessage = String.Format("[{0}] Dados do usuário obtidos - Username: {1}, Email: {2}, ID Externo: {3}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), username, email, externalUserId)
            Console.WriteLine(logMessage)

            logMessage = String.Format("[{0}] Obtendo token de autenticação...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)

            Dim authToken As String = CCC_RevvoLearning_Auth(clientId, clientSecret)

            If String.IsNullOrEmpty(authToken) Then
                logMessage = String.Format("[{0}] ERRO: Falha na autenticação - Token não obtido", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                Console.WriteLine(logMessage)
                Return "ERRO: Falha na autenticação"
            End If

            logMessage = String.Format("[{0}] Token obtido com sucesso", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)
            
            Dim jsonData As String = String.Format(
                "{{""user"": {{""username"": ""{0}"", ""firstname"": ""{1}"", ""lastname"": ""{2}"", ""email"": ""{3}"", ""department"": ""{4}"", ""institution"": ""{5}"", ""phone1"": ""{6}""}}}}",
                username, firstname, lastname, email, department, institution, phone1
            )

            logMessage = String.Format("[{0}] Enviando requisição para atualizar usuário na API RevvoLearning...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)

            Try
                Using client As New WebClient()
                    client.Headers.Add("Content-Type", "application/json")
                    client.Headers.Add("Authorization", "Bearer " & authToken)

                    Dim responseBytes As Byte() = client.UploadData("https://dasa-poc.learningflix.net/webservice/api/v3/users/" & externalUserId, "PATCH", System.Text.Encoding.UTF8.GetBytes(jsonData))
                    Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)

                    logMessage = String.Format("[{0}] Resposta da API recebida: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), responseString)
                    Console.WriteLine(logMessage)

                    logMessage = String.Format("[{0}] Usuário atualizado com sucesso! ID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), externalUserId)
                    Console.WriteLine(logMessage)

                    Return "SUCESSO: Usuário atualizado com ID " & externalUserId

                End Using
            Catch webEx As WebException
                Dim errorResponse As String = ""
                Dim statusCode As String = ""

                If webEx.Response IsNot Nothing Then
                    Dim httpResponse As HttpWebResponse = CType(webEx.Response, HttpWebResponse)
                    statusCode = httpResponse.StatusCode.ToString() & " (" & CInt(httpResponse.StatusCode) & ")"

                    Using reader As New StreamReader(webEx.Response.GetResponseStream())
                        errorResponse = reader.ReadToEnd()
                    End Using
                End If

                logMessage = String.Format("[{0}] ERRO na API: {1} | {2} | Resposta: {3}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), statusCode, webEx.Message, errorResponse)
                Console.WriteLine(logMessage)
                Return "ERRO: Falha na API - " & statusCode & " | " & webEx.Message & " | Resposta: " & errorResponse

            Catch ex As Exception
                logMessage = String.Format("[{0}] ERRO geral: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ex.Message)
                Console.WriteLine(logMessage)
                Return "ERRO: " & ex.Message
            End Try

        Else
            logMessage = String.Format("[{0}] ERRO: Conta não encontrada para UID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), UID_UNSAccountB)
            Console.WriteLine(logMessage)
            Return "ERRO: Conta não encontrada"
        End If

    Catch generalEx As Exception
        logMessage = String.Format("[{0}] ERRO CRÍTICO: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), generalEx.Message)
        Console.WriteLine(logMessage)
        Return "ERRO CRÍTICO: " & generalEx.Message
    End Try
End Function

' --- Exemplo de como usaria esta função em outro script ---
' Sub Main() ' Ou o contexto onde você chamaria esta função no One Identity Manager
'     ' Exemplo 1: Atualizar usuário específico
'     Dim uidAccount As String = "UID-12345-ABCDE"
'     
'     Try
'         CCC_RevvoLearning_UpdateUser(uidAccount)
'         Console.WriteLine("Usuário atualizado com sucesso: " & uidAccount)
'     Catch ex As Exception
'         Console.WriteLine("Erro ao atualizar usuário: " & ex.Message)
'     End Try
'     
'     ' Exemplo 2: Atualizar usuários modificados recentemente
'     Dim f = Session.SqlFormatter
'     Dim recentlyModified = f.Comparison("XDateUpdated", ">", DateTime.Now.AddDays(-1), ValType.Date)
'     Dim modifiedAccountsCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(recentlyModified).SelectNonLobs)
'     
'     Console.WriteLine("Encontradas " & modifiedAccountsCol.Count & " contas modificadas nas últimas 24h")
'     
'     For Each account As IEntity In modifiedAccountsCol
'         Dim uid As String = account.GetValue("UID_UNSAccountB").String
'         Dim externalId As String = account.GetValue("UID_ExternalUser").String
'         
'         If Not String.IsNullOrEmpty(externalId) Then
'             Try
'                 Console.WriteLine("Atualizando: " & uid)
'                 CCC_RevvoLearning_UpdateUser(uid)
'                 System.Threading.Thread.Sleep(500) ' Pausa entre atualizações
'             Catch ex As Exception
'                 Console.WriteLine("Falha ao atualizar " & uid & ": " & ex.Message)
'             End Try
'         Else
'             Console.WriteLine("Pulando " & uid & " (sem ID externo)")
'         End If
'     Next
'     
'     ' Exemplo 3: Atualizar com verificação de mudanças
'     Dim checkUID As String = "UID-CHECK-CHANGES"
'     
'     ' Busca dados atuais no RevvoLearning
'     Dim currentUserData As String = CCC_RevvoLearning_GetUserByOIM(checkUID)
'     
'     If Not String.IsNullOrEmpty(currentUserData) Then
'         Dim currentJson As JObject = JObject.Parse(currentUserData)
'         Dim currentUser As JObject = currentJson("user")
'         
'         ' Busca dados no OIM
'         Dim accountQuery = f.UidComparison("UID_UNSAccountB", checkUID)
'         Dim accountCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(accountQuery).SelectNonLobs)
'         
'         If accountCol.Count > 0 Then
'             Dim oimAccount As IEntity = accountCol(0)
'             Dim oimEmail As String = oimAccount.GetValue("Ident_PersonalEmail").String
'             Dim revvoEmail As String = currentUser("email").ToString()
'             
'             If oimEmail <> revvoEmail Then
'                 Console.WriteLine("Email mudou de '" & revvoEmail & "' para '" & oimEmail & "'. Atualizando...")
'                 CCC_RevvoLearning_UpdateUser(checkUID)
'             Else
'                 Console.WriteLine("Nenhuma mudança detectada para: " & checkUID)
'             End If
'         End If
'     End If
' End Sub
