#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
#End If

Public Function CCC_RevvoLearning_Create(ByVal UID_UNSAccountB As String) As String
    Dim logMessage As String = ""

    Dim clientId As String = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\ClientId")
    Dim clientSecret As String = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\ClientSecret")

    Try
        logMessage = String.Format("[{0}] Iniciando criação de usuário para UID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), UID_UNSAccountB)
        Console.WriteLine(logMessage)

        Dim f = Session.SqlFormatter
        Dim queryUNSAccountB = f.UidComparison("UID_UNSAccountB", UID_UNSAccountB)
        Dim UNSAccountBCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(queryUNSAccountB).SelectNonLobs)

    If UNSAccountBCol.Count > 0 Then
        Dim UNSAccountB As IEntity = UNSAccountBCol(0)

        ' Campos principais
        Dim username As String = UNSAccountB.GetValue("CCC_CustomProperty11").String
        Dim firstname As String = UNSAccountB.GetValue("FirstName").String
        Dim lastname As String = UNSAccountB.GetValue("LastName").String
        Dim email As String = UNSAccountB.GetValue("CCC_CustomProperty15").String
        Dim password As String = ""
        Dim department As String = UNSAccountB.GetValue("CCC_CustomProperty03").String
        Dim institution As String = UNSAccountB.GetValue("CCC_CustomProperty19").String
        Dim phone1 As String = UNSAccountB.GetValue("CCC_CustomProperty25").String

        ' Campos customizados conforme mapeamento
        Dim nome As String = UNSAccountB.GetValue("AccountName").String
        Dim denominacao_de_unidade_organizacional As String = UNSAccountB.GetValue("CCC_CustomProperty03").String
        Dim denominacao_do_grupo_de_empregados As String = UNSAccountB.GetValue("CCC_CustomProperty08").String
        Dim denominacao_do_subgrupo_de_empregados As String = UNSAccountB.GetValue("CCC_CustomProperty28").String
        Dim sexo As String = UNSAccountB.GetValue("CCC_CustomProperty13").String
        Dim data_de_nascimento As String = UNSAccountB.GetValue("CCC_CustomProperty14").String
        Dim data_de_admissao As String = UNSAccountB.GetValue("CCC_CustomProperty04").String
        Dim status_do_colaborador As String = UNSAccountB.GetValue("AccountDisabled").String
        Dim descricao_de_cargo As String = UNSAccountB.GetValue("CCC_CustomProperty12").String
        Dim descricao_do_estabelecimento_regional As String = UNSAccountB.GetValue("CCC_CustomProperty10").String
        Dim descricao_da_empresa As String = UNSAccountB.GetValue("CCC_CustomProperty19").String
        Dim descricao_da_marca As String = UNSAccountB.GetValue("CCC_CustomProperty05").String
        Dim descricao_do_negocio As String = UNSAccountB.GetValue("CCC_CustomProperty09").String
        Dim descricao_do_centro_de_custo As String = UNSAccountB.GetValue("CCC_CustomProperty21").String
        Dim gestor_imediato As String = UNSAccountB.GetValue("CCC_CustomProperty06").String
        Dim telefone As String = UNSAccountB.GetValue("CCC_CustomProperty25").String
        Dim cidade As String = UNSAccountB.GetValue("CCC_CustomProperty22").String
        Dim cpf As String = UNSAccountB.GetValue("CCC_CustomProperty11").String
        Dim crm_regiao As String = UNSAccountB.GetValue("CCC_CustomProperty26").String
        Dim uf As String = UNSAccountB.GetValue("CCC_CustomProperty23").String

        ' Construir customfields array
        Dim customFieldsList As New List(Of String)

        If Not String.IsNullOrEmpty(nome) Then
            customFieldsList.Add(String.Format("{{""type"": ""nome"", ""value"": ""{0}""}}", nome))
        End If
        If Not String.IsNullOrEmpty(denominacao_de_unidade_organizacional) Then
            customFieldsList.Add(String.Format("{{""type"": ""denominacao_de_unidade_organizacional"", ""value"": ""{0}""}}", denominacao_de_unidade_organizacional))
        End If
        If Not String.IsNullOrEmpty(denominacao_do_grupo_de_empregados) Then
            customFieldsList.Add(String.Format("{{""type"": ""denominacao_do_grupo_de_empregados"", ""value"": ""{0}""}}", denominacao_do_grupo_de_empregados))
        End If
        If Not String.IsNullOrEmpty(denominacao_do_subgrupo_de_empregados) Then
            customFieldsList.Add(String.Format("{{""type"": ""denominacao_do_subgrupo_de_empregados"", ""value"": ""{0}""}}", denominacao_do_subgrupo_de_empregados))
        End If
        If Not String.IsNullOrEmpty(sexo) Then
            customFieldsList.Add(String.Format("{{""type"": ""sexo"", ""value"": ""{0}""}}", sexo))
        End If
        If Not String.IsNullOrEmpty(data_de_nascimento) Then
            customFieldsList.Add(String.Format("{{""type"": ""data_de_nascimento"", ""value"": ""{0}""}}", data_de_nascimento))
        End If
        If Not String.IsNullOrEmpty(data_de_admissao) Then
            customFieldsList.Add(String.Format("{{""type"": ""data_de_admissao"", ""value"": ""{0}""}}", data_de_admissao))
        End If
        If Not String.IsNullOrEmpty(status_do_colaborador) Then
            customFieldsList.Add(String.Format("{{""type"": ""status_do_colaborador"", ""value"": ""{0}""}}", status_do_colaborador))
        End If
        If Not String.IsNullOrEmpty(descricao_de_cargo) Then
            customFieldsList.Add(String.Format("{{""type"": ""descricao_de_cargo"", ""value"": ""{0}""}}", descricao_de_cargo))
        End If
        If Not String.IsNullOrEmpty(descricao_do_estabelecimento_regional) Then
            customFieldsList.Add(String.Format("{{""type"": ""descricao_do_estabelecimento_regional"", ""value"": ""{0}""}}", descricao_do_estabelecimento_regional))
        End If
        If Not String.IsNullOrEmpty(descricao_da_empresa) Then
            customFieldsList.Add(String.Format("{{""type"": ""descricao_da_empresa"", ""value"": ""{0}""}}", descricao_da_empresa))
        End If
        If Not String.IsNullOrEmpty(descricao_da_marca) Then
            customFieldsList.Add(String.Format("{{""type"": ""descricao_da_marca"", ""value"": ""{0}""}}", descricao_da_marca))
        End If
        If Not String.IsNullOrEmpty(descricao_do_negocio) Then
            customFieldsList.Add(String.Format("{{""type"": ""descricao_do_negocio"", ""value"": ""{0}""}}", descricao_do_negocio))
        End If
        If Not String.IsNullOrEmpty(descricao_do_centro_de_custo) Then
            customFieldsList.Add(String.Format("{{""type"": ""descricao_do_centro_de_custo"", ""value"": ""{0}""}}", descricao_do_centro_de_custo))
        End If
        If Not String.IsNullOrEmpty(gestor_imediato) Then
            customFieldsList.Add(String.Format("{{""type"": ""gestor_imediato"", ""value"": ""{0}""}}", gestor_imediato))
        End If
        If Not String.IsNullOrEmpty(telefone) Then
            customFieldsList.Add(String.Format("{{""type"": ""telefone"", ""value"": ""{0}""}}", telefone))
        End If
        If Not String.IsNullOrEmpty(cidade) Then
            customFieldsList.Add(String.Format("{{""type"": ""cidade"", ""value"": ""{0}""}}", cidade))
        End If
        If Not String.IsNullOrEmpty(cpf) Then
            customFieldsList.Add(String.Format("{{""type"": ""cpf"", ""value"": ""{0}""}}", cpf))
        End If
        If Not String.IsNullOrEmpty(crm_regiao) Then
            customFieldsList.Add(String.Format("{{""type"": ""crm_regiao"", ""value"": ""{0}""}}", crm_regiao))
        End If
        If Not String.IsNullOrEmpty(uf) Then
            customFieldsList.Add(String.Format("{{""type"": ""uf"", ""value"": ""{0}""}}", uf))
        End If

        Dim customFieldsJson As String = "[" & String.Join(",", customFieldsList.ToArray()) & "]"

        ' Atualizar JSON para incluir customfields
        Dim jsonData As String = String.Format(
            "{{""user"": {{""nome"": ""{0}"", ""firstname"": ""{1}"", ""lastname"": ""{2}"", ""email"": ""{3}"", ""password"": """", ""createpassword"": true, ""department"": ""{4}"", ""institution"": ""{5}"", ""phone1"": ""{6}"", ""customfields"": {7}}}}}",
            nome, firstname, lastname, email, department, institution, phone1, customFieldsJson
        )

        ' Atualizar JSON para update também
        Dim updateJsonData As String = String.Format(
            "{{""user"": {{""nome"": ""{0}"", ""firstname"": ""{1}"", ""lastname"": ""{2}"", ""email"": ""{3}"", ""department"": ""{4}"", ""institution"": ""{5}"", ""phone1"": ""{6}"", ""customfields"": {7}}}}}",
            nome, firstname, lastname, email, department, institution, phone1, customFieldsJson
        )

        logMessage = String.Format("[{0}] Dados do usuário obtidos - Nome: {1}, Email: {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), nome, email)
        Console.WriteLine(logMessage)

        logMessage = String.Format("[{0}] Obtendo token de autenticação...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
        Console.WriteLine(logMessage)

        Dim authToken As String = CCC_RevvoLearning_Auth(clientId, clientSecret)

        If String.IsNullOrEmpty(authToken) Then
            logMessage = String.Format("[{0}] ERRO: Falha na autenticação - Token não obtido", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)
            Return "ERRO: Falha na autenticação"
        End If

        logMessage = String.Format("[{0}] Token obtido com sucesso", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
        Console.WriteLine(logMessage)
        
        logMessage = String.Format("[{0}] Enviando requisição para criar usuário na API RevvoLearning...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
        Console.WriteLine(logMessage)

        Try
            Using client As New WebClient()
                client.Headers.Add("Content-Type", "application/json")
                client.Headers.Add("Authorization", "Bearer " & authToken)

                Dim responseBytes As Byte() = client.UploadData("https://dasa-poc.learningflix.net/webservice/api/v3/users", "POST", System.Text.Encoding.UTF8.GetBytes(jsonData))
                Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)

                logMessage = String.Format("[{0}] Resposta da API recebida: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), responseString)
                Console.WriteLine(logMessage)

                Dim jsonResponse As JObject = JObject.Parse(responseString)
                Dim userId As String = jsonResponse("id").ToString()

                logMessage = String.Format("[{0}] Usuário criado com sucesso! ID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), userId)
                Console.WriteLine(logMessage)

                ' Atualizar o campo CCC_CustomProperty24 usando o padrão correto do IDM
                Try
                    ' Criar uma entidade completa para atualização (similar ao Person_Import.vb)
                    Dim fullEntity As IEntity = UNSAccountB.Create(Session)

                    ' Verificar se o valor atual é diferente do novo valor
                    If DbVal.Compare(UNSAccountB.GetRaw("CCC_CustomProperty24"), userId, ValType.String) <> 0 Then
                        fullEntity.PutValue("CCC_CustomProperty24", userId)

                        logMessage = String.Format("[{0}] Atualizando campo CCC_CustomProperty24 com valor: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), userId)
                        Console.WriteLine(logMessage)

                        ' Salvar apenas se houver diferença
                        If fullEntity.IsDifferent Then
                            fullEntity.Save(Session)
                            logMessage = String.Format("[{0}] ID do usuário salvo com sucesso no campo CCC_CustomProperty24", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                            Console.WriteLine(logMessage)
                        Else
                            logMessage = String.Format("[{0}] Campo CCC_CustomProperty24 já possui o valor correto", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                            Console.WriteLine(logMessage)
                        End If
                    Else
                        logMessage = String.Format("[{0}] Campo CCC_CustomProperty24 já possui o valor correto: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), userId)
                        Console.WriteLine(logMessage)
                    End If

                Catch saveEx As Exception
                    logMessage = String.Format("[{0}] ERRO ao salvar ID no IDM: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), saveEx.Message)
                    Console.WriteLine(logMessage)
                    Return "ERRO: Usuário criado na API mas falha ao salvar ID no IDM - " & saveEx.Message
                End Try

                Return "SUCESSO: Usuário criado com ID " & userId

            End Using
        Catch webEx As WebException
            Dim errorResponse As String = ""
            Dim statusCode As String = ""

            If webEx.Response IsNot Nothing Then
                Dim httpResponse As HttpWebResponse = CType(webEx.Response, HttpWebResponse)
                statusCode = httpResponse.StatusCode.ToString() & " (" & CInt(httpResponse.StatusCode) & ")"

                Using reader As New StreamReader(webEx.Response.GetResponseStream())
                    errorResponse = reader.ReadToEnd()
                End Using
            End If

            Return "ERRO: Falha na API - " & statusCode & " | " & webEx.Message & " | Resposta: " & errorResponse

        Catch ex As Exception
            logMessage = String.Format("[{0}] ERRO geral: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ex.Message)
            Console.WriteLine(logMessage)
            Return "ERRO: " & ex.Message
        End Try

    Else
        logMessage = String.Format("[{0}] ERRO: Conta não encontrada para UID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), UID_UNSAccountB)
        Console.WriteLine(logMessage)
        Return "ERRO: Conta não encontrada"
    End If

    Catch generalEx As Exception
        logMessage = String.Format("[{0}] ERRO CRÍTICO: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), generalEx.Message)
        Console.WriteLine(logMessage)
        Return "ERRO CRÍTICO: " & generalEx.Message
    End Try
End Function
